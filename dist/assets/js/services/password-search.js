// 密码搜索服务
class PasswordSearchService {
    constructor() {
        this.searchHistory = this.loadSearchHistory();
        this.searchIndex = new Map(); // 搜索索引
        this.stopWords = new Set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这']);
    }

    // 构建搜索索引
    buildSearchIndex(passwords) {
        this.searchIndex.clear();

        passwords.forEach(password => {
            const searchableText = this.extractSearchableText(password);
            const keywords = this.extractKeywords(searchableText);

            keywords.forEach(keyword => {
                if (!this.searchIndex.has(keyword)) {
                    this.searchIndex.set(keyword, new Set());
                }
                this.searchIndex.get(keyword).add(password.id);
            });
        });
    }

    // 提取可搜索文本
    extractSearchableText(password) {
        const texts = [];
        
        if (password.title) texts.push(password.title);
        if (password.username) texts.push(password.username);
        if (password.notes) texts.push(password.notes);
        if (password.url) texts.push(password.url);
        
        return texts.join(' ').toLowerCase();
    }

    // 提取关键词
    extractKeywords(text) {
        const keywords = new Set();
        
        // 中文分词（简单实现）
        const chineseWords = text.match(/[\u4e00-\u9fa5]+/g) || [];
        chineseWords.forEach(word => {
            if (word.length >= 2 && !this.stopWords.has(word)) {
                keywords.add(word);
                // 添加子字符串
                for (let i = 0; i < word.length - 1; i++) {
                    for (let j = i + 2; j <= word.length; j++) {
                        const substr = word.substring(i, j);
                        if (!this.stopWords.has(substr)) {
                            keywords.add(substr);
                        }
                    }
                }
            }
        });

        // 英文单词
        const englishWords = text.match(/[a-zA-Z]+/g) || [];
        englishWords.forEach(word => {
            if (word.length >= 2) {
                keywords.add(word.toLowerCase());
            }
        });

        // 数字
        const numbers = text.match(/\d+/g) || [];
        numbers.forEach(num => keywords.add(num));

        return Array.from(keywords);
    }

    // 执行搜索
    search(query, passwords, options = {}) {
        const {
            fuzzy = true,
            maxResults = 50,
            sortBy = 'relevance', // 'relevance', 'date', 'title'
            searchField = 'all' // 'all', 'title', 'username', 'notes'
        } = options;

        if (!query || query.trim().length === 0) {
            return passwords;
        }

        const searchQuery = query.toLowerCase().trim();
        this.addToSearchHistory(searchQuery);

        // 根据搜索字段过滤
        let results = this.filterByField(searchQuery, passwords, searchField);

        // 精确匹配
        let exactMatches = this.exactSearch(searchQuery, results);

        // 模糊匹配
        let fuzzyMatches = [];
        if (fuzzy && exactMatches.length < maxResults) {
            fuzzyMatches = this.fuzzySearch(searchQuery, results, exactMatches);
        }

        // 合并结果
        let allResults = [...exactMatches, ...fuzzyMatches];

        // 去重
        const seen = new Set();
        allResults = allResults.filter(item => {
            const id = item.password ? item.password.id : item.id;
            if (seen.has(id)) return false;
            seen.add(id);
            return true;
        });

        // 排序
        allResults = this.sortResults(allResults, sortBy);

        // 限制结果数量
        return allResults.slice(0, maxResults);
    }

    // 根据字段过滤
    filterByField(query, passwords, searchField) {
        if (searchField === 'all') {
            return passwords;
        }

        return passwords.filter(password => {
            const fieldValue = password[searchField];
            return fieldValue && fieldValue.toLowerCase().includes(query);
        });
    }

    // 精确搜索
    exactSearch(query, passwords) {
        const results = [];

        passwords.forEach(password => {
            const searchableText = this.extractSearchableText(password);
            
            if (searchableText.includes(query)) {
                const score = this.calculateExactScore(query, searchableText, password);
                results.push({
                    password,
                    score,
                    matchedText: this.extractMatchedText(query, searchableText)
                });
            }
        });

        return results;
    }

    // 模糊搜索
    fuzzySearch(query, passwords, excludePasswords = []) {
        const excludeIds = new Set(excludePasswords.map(item => item.password.id));
        const results = [];

        passwords.forEach(password => {
            if (excludeIds.has(password.id)) return;

            const searchableText = this.extractSearchableText(password);
            const score = this.calculateFuzzyScore(query, searchableText, password);

            if (score > 0.3) { // 模糊匹配阈值
                results.push({
                    password,
                    score,
                    matchedText: this.extractMatchedText(query, searchableText)
                });
            }
        });

        return results;
    }

    // 计算精确匹配分数
    calculateExactScore(query, text, password) {
        let score = 0;

        // 标题匹配权重最高
        if (password.title && password.title.toLowerCase().includes(query)) {
            score += 10;
        }

        // 用户名匹配
        if (password.username && password.username.toLowerCase().includes(query)) {
            score += 8;
        }

        // 备注匹配
        if (password.notes && password.notes.toLowerCase().includes(query)) {
            score += 6;
        }

        // URL匹配
        if (password.url && password.url.toLowerCase().includes(query)) {
            score += 4;
        }

        // 完全匹配加分
        if (text === query) {
            score += 20;
        }

        // 开头匹配加分
        if (text.startsWith(query)) {
            score += 10;
        }

        return score;
    }

    // 计算模糊匹配分数
    calculateFuzzyScore(query, text, password) {
        let score = 0;
        const queryWords = query.split(/\s+/);

        queryWords.forEach(word => {
            if (text.includes(word)) {
                score += word.length;
            }
        });

        // 字符相似度
        const similarity = this.calculateStringSimilarity(query, text);
        score += similarity * 5;

        return score;
    }

    // 计算字符串相似度
    calculateStringSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const distance = this.levenshteinDistance(longer, shorter);
        return (longer.length - distance) / longer.length;
    }

    // 计算编辑距离
    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    // 提取匹配文本
    extractMatchedText(query, text) {
        const index = text.indexOf(query);
        if (index === -1) return '';

        const start = Math.max(0, index - 20);
        const end = Math.min(text.length, index + query.length + 20);
        
        return text.substring(start, end);
    }

    // 排序结果
    sortResults(results, sortBy) {
        switch (sortBy) {
            case 'relevance':
                return results.sort((a, b) => b.score - a.score);
            case 'date':
                return results.sort((a, b) => {
                    const dateA = new Date(a.password.createdAt || 0);
                    const dateB = new Date(b.password.createdAt || 0);
                    return dateB - dateA;
                });
            case 'title':
                return results.sort((a, b) => {
                    const titleA = a.password.title || '';
                    const titleB = b.password.title || '';
                    return titleA.localeCompare(titleB);
                });
            default:
                return results;
        }
    }

    // 添加到搜索历史
    addToSearchHistory(query) {
        if (!query || query.length < 2) return;

        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);

        // 添加到开头
        this.searchHistory.unshift({
            query,
            timestamp: Date.now()
        });

        // 限制历史记录数量
        this.searchHistory = this.searchHistory.slice(0, 20);

        this.saveSearchHistory();
    }

    // 获取最近搜索
    getRecentSearches(limit = 5) {
        return this.searchHistory.slice(0, limit).map(item => item.query);
    }

    // 清空搜索历史
    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
    }

    // 保存搜索历史
    saveSearchHistory() {
        try {
            localStorage.setItem('password_search_history', JSON.stringify(this.searchHistory));
        } catch (e) {
            console.warn('无法保存密码搜索历史:', e);
        }
    }

    // 加载搜索历史
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('password_search_history');
            return history ? JSON.parse(history) : [];
        } catch (e) {
            console.warn('无法加载密码搜索历史:', e);
            return [];
        }
    }
}

// 创建全局实例
window.PasswordSearchService = PasswordSearchService;

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PasswordSearchService;
}
